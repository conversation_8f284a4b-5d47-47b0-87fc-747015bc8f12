﻿<Project Sdk="Microsoft.NET.Sdk">

	<ItemGroup>
		<PackageReference Include="FluentResults" Version="3.16.0" />
		<PackageReference Include="Google.Cloud.Storage.V1" Version="4.11.0" />
		<PackageReference Include="MassTransit" Version="8.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.0-rc.2.24474.3" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.8" />
		<PackageReference Include="EFCore.NamingConventions" Version="8.0.3" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\DataVenia.Common.Application\DataVenia.Common.Application.csproj" />
	  <ProjectReference Include="..\DataVenia.Modules.Users.Domain\DataVenia.Modules.Users.Domain.csproj" />
	</ItemGroup>
</Project>

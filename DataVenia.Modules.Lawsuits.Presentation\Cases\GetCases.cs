﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Cases.GetCases;
using DataVenia.Modules.Users.Presentation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Presentation.Cases;

internal sealed class GetCases : IEndpoint
{
    private readonly ILogger<GetCases> _logger;

    public GetCases(ILogger<GetCases> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        // passar officeId
        routeBuilder.MapGet("/offices/{officeId}/cases", async (Guid officeId, ClaimsPrincipal claims, ISender sender) =>
        {
            try
            {
                // tem que usar o office_id da empresa que o cara ta logado
                Result<IReadOnlyCollection<CaseResponse>> result = await sender.Send(new GetCasesQuery(claims.GetUserId(), officeId));

                return result.Match(
                   success =>
                   {
                       var responseObject = new
                       {
                           items = success
                       };

                       return Results.Ok(responseObject);
                   },
                   ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cases");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("office:lawsuits:read")
        .WithTags(Tags.Cases);
    }
}

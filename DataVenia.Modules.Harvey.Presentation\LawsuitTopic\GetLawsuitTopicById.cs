﻿using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Harvey.Application.LawsuitTopic;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Harvey.Presentation.LawsuitTopic;

public sealed class GetLawsuitTopicById(ILogger<GetLawsuitTopicById> _logger) : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("/harvey/lawsuit-topics/{id}", async (int id, ISender sender) =>
        {
            try
            {
                Result<GetLawsuitTopicByIdResponse> result = await sender.Send(new GetLawsuitTopicByIdQuery(id));

                return result.Match(
                    success => Results.Ok(success),
                    ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting lawsuit topic by id {Id}", id);
            }

            return Results.NotFound();
        })
        .RequireAuthorization("system:harvey:read")
        .WithTags(Tags.Harvey);
    }
}

﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Client.DeleteClient;
using DataVenia.Modules.Users.Application.Client.GetClients;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Client;

public class DeleteClient: IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapDelete("offices/{officeId}/clients/{clientId}", async (Guid officeId, Guid clientId, ISender sender) =>
            {
                Result result = await sender.Send(new DeleteClientCommand(
                    clientId));
                
                return result.Match(Results.NoContent, ApiResults.Problem);
            })
            .RequireAuthorization("office:clients:delete")
            .WithTags(Tags.Client);
    }
}

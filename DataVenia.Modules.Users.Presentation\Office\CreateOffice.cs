﻿using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Office;
using DataVenia.Modules.Users.Application.Office.CreateOffice;
using DataVenia.Modules.Users.Domain.SharedModels;

namespace DataVenia.Modules.Users.Presentation.Office;
internal sealed class CreateOffice : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("offices", async (CreateOfficeRequest request, ISender sender) =>
        {
            Result<Guid> result = await sender.Send(new CreateOfficeCommand(
                request.Name,
                request.Website,
                request.Cnpj,
                request.Contacts,
                request.Addresses
                ));

            return result.Match(id => Results.Ok(new { Id = id }), ApiResults.Problem);
        })
        .RequireAuthorization("system:administrator:all")
        .WithTags(Tags.Office);
    }

}
    public sealed class CreateOfficeRequest
    {
        public string Name { get; init; }
        public string Website { get; init; }
        public string Cnpj { get; init; }
        public IReadOnlyCollection<Contact> Contacts { get; init; }
        public IReadOnlyCollection<Address> Addresses { get; init; }
    }

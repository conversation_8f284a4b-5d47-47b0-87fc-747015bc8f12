﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Client.GetClients;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Client;
public sealed class GetClients: IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("offices/{officeId}/clients", async (Guid officeId, ISender sender) =>
        {
            Result<IReadOnlyCollection<GetClientsResponse>> result = await sender.Send(new GetClientsQuery(
                officeId));

            return result.Match(
                success =>
                {
                    var responseObject = new
                    {
                        items = success
                    };

                    return Results.Ok(responseObject);
                },
                ApiResults.Problem);
        })
        .RequireAuthorization("office:clients:read")
        .WithTags(Tags.Client);
    }
}

﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Calendar.Application.Appointment.CreateAppointment;
using DataVenia.Modules.Calendar.Application.Appointments.UpdateAppointmentPartial.cs;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Calendar.Presentation.Appointments;

public sealed class UpdateAppointmentPatch : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPatch("/offices/{officeId}/appointments/{appointmentId}", async (
                Guid officeId,
                Guid appointmentId,
                UpdateAppointmentPatchRequest request,
                ClaimsPrincipal claims,
                ISender sender) =>
            {
                var command = new UpdateAppointmentPatchCommand(
                    UserId: claims.GetUserId(),
                    OfficeId: officeId,
                    AppointmentId: appointmentId,
                    Type: request.Type,
                    Name: request.Name,
                    Description: request.Description,
                    ResponsibleLawyerId: request.ResponsibleLawyerId,
                    Recurrence: request.Recurrence,
                    ParticipantLawyersIds: request.ParticipantLawyersIds,
                    Alerts: request.Alerts,
                    StatusId: request.StatusId
                );

                Result result = await sender.Send(command);

                return result.Match(Results.NoContent, ApiResults.Problem);
            })
            .RequireAuthorization("office:appointments:update")
            .WithTags(Tags.Calendar);
    }
}

public sealed class UpdateAppointmentPatchRequest
{
    public string? Type { get; init; }
    public string? Name { get; init; }
    public string? Description { get; init; }
    public Guid? ResponsibleLawyerId { get; init; }
    public RecurrenceCommand? Recurrence { get; init; }
    public List<Guid>? ParticipantLawyersIds { get; init; }
    public List<TimeSpan>? Alerts { get; init; }
    public Guid? StatusId { get; init; }
}

using System.Globalization;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.AssociationHub.Application.Associations.RemoveAssociation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.AssociationHub.Presentation.Associations;

internal sealed class RemoveAssociation : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapDelete("/associations", async (RemoveAssociationRequest request, ISender sender) =>
        {
            var commandAssociations = request.Associations.Select(a => new AssociationTarget(
                a.TargetEntityType,
                a.TargetEntityIds)).ToList();

            var result = await sender.Send(new RemoveAssociationCommand(
                request.EntityType,
                request.EntityId,
                commandAssociations));

            if (result.IsSuccess)
                return Results.NoContent();
            else
            {
                var error = result.Errors.FirstOrDefault();
                int statusCode = error?.Metadata != null && error.Metadata.TryGetValue("StatusCode", out var codeObj)
                    ? Convert.ToInt32(codeObj, CultureInfo.InvariantCulture)
                    : 500;

                return Results.Problem(detail: string.Join("; ", result.Errors.Select(e => e.Message)), statusCode: statusCode);
            }
        })
        .RequireAuthorization("system:associations:delete")
        .WithTags(Tags.Associations);
    }
internal sealed record RemoveAssociationRequest
{
    public string EntityType { get; init; } = string.Empty;
    public Guid EntityId { get; init; }
    public IReadOnlyCollection<AssociationTargetRequest> Associations { get; init; } = [];
}

internal sealed record AssociationTargetRequest
{
    public string TargetEntityType { get; init; } = string.Empty;
    public IReadOnlyCollection<Guid> TargetEntityIds { get; init; } = [];
}
}


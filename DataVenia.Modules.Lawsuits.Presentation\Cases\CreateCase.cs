﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.CreateCase;
using DataVenia.Modules.Lawsuits.Domain.DTOs;
using DataVenia.Modules.Users.Presentation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Presentation.Cases;

internal sealed class CreateCase : IEndpoint
{
    private readonly ILogger<CreateCase> _logger;

    public CreateCase(ILogger<CreateCase> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/offices/{officeId}/cases", async (Guid officeId, CreateCaseRequest request, ClaimsPrincipal claims, ISender sender) =>
        {
            try
            {
                // tem que usar o office_id da empresa que o cara ta logado
                Result<Guid> result = await sender.Send(new CreateCaseCommand(
                    request.Title,
                    request.FolderId,
                    request.Parties,
                    request.Description,
                    request.Observations,
                    request.CauseValue,
                    request.ConvictionValue,
                    request.ResponsibleIds,
                    request.Access,
                    claims.GetUserId(),
                    officeId));

                return result.Match(id => Results.Ok(new { Id = id }), ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating case");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("office:lawsuits:create")
        .WithTags(Tags.Cases);
    }

}
public sealed class CreateCaseRequest
{
    public string Title { get; init; }
    public Guid? FolderId { get; init; }
    public List<PartyDto> Parties { get; init; }
    public List<Guid> ResponsibleIds { get; init; }
    public string Description { get; init; }
    public string Observations { get; init; }
    public decimal CauseValue { get; init; }
    public decimal ConvictionValue { get; init; }
    public string Access { get; init; }

}

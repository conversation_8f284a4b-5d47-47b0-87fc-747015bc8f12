﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Calendar.Application.Appointment.CreateAppointment;
using DataVenia.Modules.Calendar.Domain.Appointments;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Calendar.Presentation.Appointment;

internal sealed class CreateAppointment : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/offices/{officeId}/appointments", async (Guid officeId, CreateAppointmentRequest request, ISender sender) =>
        {
            Result<Guid> result = await sender.Send(new CreateAppointmentCommand(
                officeId,
                request.Type,
                request.Name,
                request.Description,
                request.ResponsibleLawyerId,
                request.Recurrence != null ? new RecurrenceCommand(
                    request.Recurrence.DaysOfWeek, 
                    request.Recurrence.DaysOfMonth,
                    request.Recurrence.Frequency,
                    request.Recurrence.StartDate,
                    request.Recurrence.EndDate,
                    request.Recurrence.StartTime,
                    request.Recurrence.EndTime) : null,
                request.OwnerLawyerId,
                officeId,
                request.ParticipantLawyersIds,
                request.Alerts,
                request.StatusId
                ));

            return result.Match(id => Results.Ok(new {Id = id}), ApiResults.Problem);
        })
        .RequireAuthorization("office:appointments:write")
        .WithTags(Tags.Calendar);
    }

}
public sealed class CreateAppointmentRequest
{
    public string Type { get; init; }

    public string Name { get; init; }
    public string Description { get; init; }
    public Guid ResponsibleLawyerId { get; init; }
    public RecurrenceRequest? Recurrence { get; init; }
    public Guid OwnerLawyerId { get; init; }
    public List<Guid> ParticipantLawyersIds { get; init; }
    public List<TimeSpan> Alerts { get; init; }
    public Guid? StatusId { get; init; }
}

public sealed class RecurrenceRequest
{
    public List<DayOfWeek>? DaysOfWeek {  get; init; }
    public List<int>? DaysOfMonth {  get; init; }
    public RecurrenceFrequency Frequency { get; init; }
    public DateOnly StartDate { get; init; }
    public DateOnly EndDate { get; init; }
    public TimeOnly StartTime { get; init; }
    public TimeOnly EndTime { get; init; }
}

﻿namespace DataVenia.Common.Domain;

public abstract class Entity
{
    private readonly List<IDomainEvent> _domainEvents = [];
    public DateTime? DeletedAt { get; private set; }

    protected Entity()
    {
    }

    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.ToList();

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    protected void Raise(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void SoftDelete()
    {
        DeletedAt = DateTime.UtcNow;
    }
}

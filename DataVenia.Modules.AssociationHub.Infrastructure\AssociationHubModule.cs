using DataVenia.Common.Infrastructure.Interceptors;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.AssociationHub.Application.Abstractions.Data;
using DataVenia.Modules.AssociationHub.Application.Services;
using DataVenia.Modules.AssociationHub.Domain.AssociationRequest;
using DataVenia.Modules.AssociationHub.Domain.EntityConfiguration;
using DataVenia.Modules.AssociationHub.Domain.Services;
using DataVenia.Modules.AssociationHub.Infrastructure.AssociationRequest;
using DataVenia.Modules.AssociationHub.Infrastructure.Configuration;
using DataVenia.Modules.AssociationHub.Infrastructure.Database;
using DataVenia.Modules.AssociationHub.Infrastructure.Services;
using DataVenia.Modules.AssociationHub.Infrastructure.Workers;
using DataVenia.Modules.AssociationHub.Presentation.Consumers;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DataVenia.Modules.AssociationHub.Infrastructure;

public static class AssociationHubModule
{
    public static IServiceCollection AddAssociationHubModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddInfrastructure(configuration);

        services.AddEndpoints(Presentation.AssemblyReference.Assembly);

        return services;
    }

    public static IBusRegistrationConfigurator RegisterAssociationHubModuleConsumer(
        this IBusRegistrationConfigurator configurator)
    {
        configurator = configurator ?? throw new ArgumentNullException(nameof(configurator));

        configurator.AddConsumer<CreateAssociationEventConsumer>();
        configurator.AddConsumer<RemoveAssociationEventConsumer>();

        return configurator;
    }

    private static void AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<AssociationHubDbContext>((sp, options) =>
            options
                .UseNpgsql(
                    configuration.GetConnectionString("Database"),
                    npgsqlOptions => npgsqlOptions
                        .MigrationsHistoryTable(HistoryRepository.DefaultTableName, Schemas.AssociationHub))
                .AddInterceptors(sp.GetRequiredService<PublishDomainEventsInterceptor>())
                .UseSnakeCaseNamingConvention());

        services.AddScoped<IUnitOfWork>(sp => sp.GetRequiredService<AssociationHubDbContext>());

        services.AddScoped<IAssociationRequestRepository, AssociationRequestRepository>();

        services.AddSingleton<EntityConfigurationRegistry>(EntityConfigurationSetup.CreateDefaultRegistry());

        services.AddHttpClient<IEntityHttpService, EntityHttpService>(client =>
        {
            client.BaseAddress = new Uri(configuration["AssociationHub:BaseUrl"] ?? "http://localhost:5163");
            client.DefaultRequestHeaders.Add("User-Agent", "AssociationHub/1.0");
        });

        services.AddScoped<IAssociationProcessingService, AssociationProcessingService>();

        services.AddHostedService<AssociationProcessingWorker>();
    }
}

﻿using System.Security.Claims;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.CreateLawsuit;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.CreateLawsuitByCnj;
using DataVenia.Modules.Users.Presentation;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Presentation.Lawsuits;
internal sealed class CreateLawsuitByCnj : IEndpoint
{
    private readonly ILogger<CreateLawsuitByCnj> _logger;

    public CreateLawsuitByCnj(ILogger<CreateLawsuitByCnj> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPut("/offices/{officeId}/lawsuits", async (Guid officeId, CreateLawsuitByCnjRequest request, ClaimsPrincipal claims, ISender sender) =>
            {
                try
                {
                    Result<Guid> result = await sender.Send(new CreateLawsuitByCnjCommand(
                        claims.GetUserId(),
                        officeId,
                        request.Cnj,
                        request.Title,
                        request.LawsuitTypeId,
                        request.ResponsibleIds,
                        request.Description,
                        request.EvolvedFromCaseId,
                        request.GroupingCaseId,
                        request.MonitoringEnabled));

                    return result.Match(id => Results.Ok(new
                    {
                        Id = id
                    }), ApiResults.Problem);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating lawsuit");
                    return Results.InternalServerError();
                }
            })
            .RequireAuthorization("office:lawsuits:create")
            .WithTags(Tags.Lawsuits);
    }

    private sealed class CreateLawsuitByCnjRequest
    {
        public string Cnj { get; init; }
        public string Title { get; init; }
        public string? LawsuitTypeId { get; init; }
        public List<Guid> ResponsibleIds { get; init; }
        public string? Description { get; init; }
        public Guid? EvolvedFromCaseId { get; init; }
        public Guid? GroupingCaseId { get; init; }
        public bool MonitoringEnabled { get; init; }
    }
}

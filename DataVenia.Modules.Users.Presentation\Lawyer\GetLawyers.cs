﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Lawyer.GetLawyers;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Lawyer;
public class GetLawyers : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("offices/{officeId}/lawyers", async (Guid officeId, ClaimsPrincipal claims, ISender sender) =>
        {
            Result<IReadOnlyCollection<GetLawyersResponse>> result = await sender.Send(new GetLawyersQuery(officeId));

            return result.Match(
                success =>
                {
                    var responseObject = new
                    {
                        items = success
                    };

                    return Results.Ok(responseObject);
                },
                ApiResults.Problem);
        })
        .RequireAuthorization("office:users:read")
        .WithTags(Tags.Users);
    }
}

﻿using DataVenia.Common.Application.Authorization;
using DataVenia.Common.Contracts.Lawyer;
using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Common.Infrastructure.Interceptors;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Application.Abstractions.Identity;
using DataVenia.Modules.Users.Application.ClientCompany;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.Client;
using DataVenia.Modules.Users.Domain.ClientCompany;
using DataVenia.Modules.Users.Domain.Company;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.Oab;
using DataVenia.Modules.Users.Domain.Office;
using DataVenia.Modules.Users.Domain.Outbox;
using DataVenia.Modules.Users.Domain.SignUpToken;
using DataVenia.Modules.Users.Domain.Users;
using DataVenia.Modules.Users.Infrastructure.Authorization;
using DataVenia.Modules.Users.Infrastructure.Client;
using DataVenia.Modules.Users.Infrastructure.ClientCompany;
using DataVenia.Modules.Users.Infrastructure.Company;
using DataVenia.Modules.Users.Infrastructure.Database;
using DataVenia.Modules.Users.Infrastructure.Identity;
using DataVenia.Modules.Users.Infrastructure.IntermediateClasses;
using DataVenia.Modules.Users.Infrastructure.Lawyers;
using DataVenia.Modules.Users.Infrastructure.Oab;
using DataVenia.Modules.Users.Infrastructure.Office;
using DataVenia.Modules.Users.Infrastructure.Outbox;
using DataVenia.Modules.Users.Infrastructure.SignUpTokens;
using DataVenia.Modules.Users.Infrastructure.UserGlobalRole;
using DataVenia.Modules.Users.Infrastructure.Users;
using DataVenia.Modules.Users.Presentation;
using DataVenia.Modules.Users.Presentation.Consumers;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace DataVenia.Modules.Users.Infrastructure;

public static class UsersModule
{
    public static IServiceCollection AddUsersModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddInfrastructure(configuration);

        services.AddEndpoints(AssemblyReference.Assembly);

        return services;
    }
    
    public static IBusRegistrationConfigurator RegisterUsersModuleConsumer(
        this IBusRegistrationConfigurator configurator)
    {
        configurator = configurator ?? throw new ArgumentNullException(nameof(configurator));
        
        configurator.AddConsumer<LawyerSignedUpEventConsumer>();

        return configurator;
    }
    
    private static void AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IPermissionService, PermissionService>();

        services.Configure<KeyCloakOptions>(configuration.GetSection("Users:KeyCloak"));

        services.AddTransient<KeyCloakAuthDelegatingHandler>();

        services
            .AddHttpClient<KeyCloakClient>()
            .AddHttpMessageHandler<KeyCloakAuthDelegatingHandler>();

        services.AddTransient(serviceProvider =>
        {
            KeyCloakOptions keyCloakOptions = serviceProvider
                .GetRequiredService<IOptions<KeyCloakOptions>>().Value;

            HttpClient httpClient = serviceProvider
                .GetRequiredService<IHttpClientFactory>()
                .CreateClient(nameof(KeyCloakClient));

            ILogger<KeyCloakClient> logger = serviceProvider.GetRequiredService<ILogger<KeyCloakClient>>();
            
            return new KeyCloakClient(httpClient, keyCloakOptions, logger);
        });

        services.AddTransient<IIdentityProviderService, IdentityProviderService>();

        services.AddDbContext<UsersDbContext>((sp, options) =>
            options
                .UseNpgsql(
                    configuration.GetConnectionString("Database"),
                    npgsqlOptions => npgsqlOptions
                        .MigrationsHistoryTable(HistoryRepository.DefaultTableName, Schemas.User))
                .AddInterceptors(sp.GetRequiredService<PublishDomainEventsInterceptor>())
                .UseSnakeCaseNamingConvention(), ServiceLifetime.Scoped);

        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<ILawyerRepository, LawyerRepository>();
        services.AddScoped<IOfficeRepository, OfficeRepository>();
        services.AddScoped<IOfficeUserRepository, OfficeUserRepository>();
        services.AddScoped<IOabRepository, OabRepository>();
        services.AddScoped<IUserGlobalRoleRepository, UserGlobalRoleRepository>();
        services.AddScoped<IClientRepository, ClientRepository>();
        services.AddScoped<ICompanyRepository, CompanyRepository>();
        services.AddScoped<IClientCompanyRepository, ClientCompanyRepository>();
        services.AddScoped<ISignUpTokenRepository, SignUpTokenRepository>();
        services.AddScoped<IOutboxRepository, OutboxRepository>();

        services.AddScoped<IOfficeLawyerFacade, OfficeUserFacade>();
        services.AddScoped<ILawyerFacade, LawyerFacade>();

        services.AddScoped<IUnitOfWork>(sp => sp.GetRequiredService<UsersDbContext>());
    }
}

﻿using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Harvey.Application.DataImport;
using DataVenia.Modules.Users.Domain.Authorization;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Harvey.Presentation.DataImport;

public sealed class ImportData(ILogger<ImportData> _logger) : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/harvey/import-data", async (ISender sender) =>
        {
            try
            {
                _logger.LogInformation("Received request to import data");
                
                Result<ImportDataResponse> result = await sender.Send(new ImportDataCommand());

                return result.Match(
                    success => Results.Ok(success),
                    ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing data");
                return Results.Problem("An error occurred while importing data");
            }
        })
        .RequireAuthorization(Permission.SystemAdministrator.Code)
        .WithTags(Tags.Harvey);
    }
}

﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Client.CreateClient;
using DataVenia.Modules.Users.Application.Company.CreateCompany;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Company;
internal sealed class CreateCompany : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/offices/{officeId}/companies", async (Guid officeId, CreateClientRequest request, ClaimsPrincipal claims, ISender sender) =>
        {
            Result<Guid> result = await sender.Send(new CreateCompanyCommand(
                officeId,
                claims.GetUserId(),
                request.Name,
                request.Cnpj
                ));

            return result.Match(id => Results.Ok(new { Id = id }), ApiResults.Problem);
        })
        .RequireAuthorization("office:clients:create")
        .WithTags(Tags.Company);
    }

}
public sealed record CreateClientRequest
{
    public string Name { get; init; }
    public string Cnpj { get; init; }
}

﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Client.GetClients;
using DataVenia.Modules.Users.Application.ClientCompany.GetClientCompanies;
using DataVenia.Modules.Users.Domain.ClientCompany;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.ClientCompany;

public class GetClientCompanies: IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("offices/{officeId}/client-companies", async (
                Guid officeId, 
                [FromQuery] Guid? clientId, 
                [FromQuery] Guid? companyId, 
                [FromQuery] ClientCompanyRole? role, ISender sender) =>
            {
                Result<IReadOnlyCollection<GetClientCompaniesResponse>> result = await sender.Send(new GetClientCompaniesQuery(
                    officeId,
                    clientId,
                    companyId,
                    role));

                return result.Match(
                    success =>
                    {
                        var responseObject = new
                        {
                            items = success
                        };

                        return Results.Ok(responseObject);
                    },
                    ApiResults.Problem);
            })
            .RequireAuthorization("office:clients:read")
            .WithTags(Tags.Client);
    }
}

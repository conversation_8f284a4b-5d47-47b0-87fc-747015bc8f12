﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Calendar.Application.Appointments.GetAppointment;
using DataVenia.Modules.Calendar.Presentation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Presentation.Lawsuits;

internal sealed class GetAppointments : IEndpoint
{
    private readonly ILogger<GetAppointments> _logger;

    public GetAppointments(ILogger<GetAppointments> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        // passar officeId
        routeBuilder.MapGet("/offices/{officeId}/appointments", async (Guid officeId, ClaimsPrincipal claims, ISender sender) =>
        {
            try
            {
                Result<IReadOnlyCollection<AppointmentResponse>> result = await sender.Send(new GetAppointmentsQuery(claims.GetUserId(), officeId));

                return result.Match(
                    success =>
                    {
                        var responseObject = new
                        {
                            items = success
                        };

                        return Results.Ok(responseObject);
                    },
                    ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("office:appointments:read")
        .WithTags(Tags.Calendar);
    }
}

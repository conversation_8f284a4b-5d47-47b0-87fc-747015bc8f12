﻿﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Lawsuits.Domain.Lawsuits;

public sealed class DataDivergence : Entity
{
    public Guid Id { get; private set; }
    public string FieldName { get; private set; }
    public string CurrentValue { get; private set; }
    public string EventValue { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public bool IsAnalyzed { get; private set; }
    public DateTime? AnalyzedAt { get; private set; }
    public string? AnalyzedBy { get; private set; }
    public bool? WasAccepted { get; private set; }

    private DataDivergence() { }

    public static DataDivergence Create(
        string fieldName,
        string currentValue,
        string eventValue)
    {
        return new DataDivergence
        {
            Id = Guid.CreateVersion7(),
            FieldName = fieldName,
            CurrentValue = currentValue,
            EventValue = eventValue,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = null,
            IsAnalyzed = false,
            WasAccepted = null
        };
    }

    public void MarkAsAnalyzed(string analyzedBy, bool wasAccepted)
    {
        IsAnalyzed = true;
        AnalyzedAt = DateTime.UtcNow;
        AnalyzedBy = analyzedBy;
        WasAccepted = wasAccepted;
    }

    public void UpdateLastSeenTime()
    {
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateEventValue(string newEventValue)
    {
        EventValue = newEventValue;
        UpdatedAt = DateTime.UtcNow;
    }

    public void ResetAnalysis()
    {
        IsAnalyzed = false;
        AnalyzedAt = null;
        AnalyzedBy = null;
        WasAccepted = null;
    }
}

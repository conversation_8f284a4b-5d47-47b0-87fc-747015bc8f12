﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Security.Claims;
//using System.Text;
//using System.Threading.Tasks;
//using DataVenia.Common.Domain;
//using DataVenia.Common.Infrastructure.Authentication;
//using DataVenia.Common.Presentation.ApiResults;
//using DataVenia.Common.Presentation.Endpoints;
//using MediatR;
//using Microsoft.AspNetCore.Builder;
//using Microsoft.AspNetCore.Http;
//using Microsoft.AspNetCore.Routing;

//namespace DataVenia.Modules.Calendar.Presentation.Status;
//internal sealed class UpdateStatus : IEndpoint
//{
//    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
//    {
//        // passar officeId
//        routeBuilder.MapGet("/offices/{officeId}/status", async (Guid officeId, ClaimsPrincipal claims, ISender sender) =>
//        {
//            try
//            {
//                Result result = await sender.Send(new UpdateStatusCommand(
//                    ));

//                return result.Match(Results.NoContent, ApiResults.Problem);
//            }
//            catch (Exception ex)
//            {
//                Console.WriteLine(ex);
//            }

//            return Results.NotFound();
//        })
//        .RequireAuthorization("office:status:read")
//        .WithTags(Tags.Calendar);
//    }
//}

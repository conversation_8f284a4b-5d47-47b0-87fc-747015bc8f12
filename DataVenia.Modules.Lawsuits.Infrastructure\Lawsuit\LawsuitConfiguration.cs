﻿using System.Text.Json;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
//using TagDomain = DataVenia.Modules.Lawsuits.Domain.Tag.Tag;

namespace DataVenia.Modules.Lawsuits.Infrastructure.Lawsuits;

internal sealed class LawsuitConfiguration : IEntityTypeConfiguration<Lawsuit>
{
    public void Configure(EntityTypeBuilder<Lawsuit> builder)
    {
        builder.ToTable("lawsuit");

        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasKey(l => l.Id);

        builder.HasQueryFilter(u => u.DeletedAt == null);

        builder.Property(l => l.Cnj)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(l => l.CreatedAt)
            .HasDefaultValueSql("NOW()");
        
        builder.Property(l => l.OfficeId)
            .HasColumnName("office_id")
            .IsRequired();

        builder.Property(l => l.LawsuitTypeId)
            .HasColumnName("lawsuit_type_id")
            .HasMaxLength(50)
            .IsRequired(false);

        builder.Property(l => l.ClassId)
            .HasColumnName("class_id")
            .HasMaxLength(50)
            .IsRequired(false);
        
        builder.Ignore(l => l.LawsuitSteps);
        
        builder.Property(l => l.MonitoringEnabled)
            .HasColumnName("monitoring_enabled")
            .IsRequired();
        
        var serializationOptions = new JsonSerializerOptions();
        
        builder.Property(l => l.MonitoringHistory)
            .HasColumnName("monitoring_history")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, serializationOptions),
                v => JsonSerializer.Deserialize<List<MonitoringHistoryEntry>>(v, serializationOptions) ?? new List<MonitoringHistoryEntry>())
            .HasDefaultValueSql("'[]'::jsonb");
        
        builder.Property(l => l.DataDivergences)
            .HasColumnName("data_divergences")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, serializationOptions),
                v => JsonSerializer.Deserialize<List<DataDivergence>>(v, serializationOptions) ?? new List<DataDivergence>())
            .HasDefaultValueSql("'[]'::jsonb");

        builder.HasMany(l => l.LawsuitDatas)
            .WithOne(ld => ld.Lawsuit)
            .HasForeignKey(ld => ld.LawsuitId)
            .OnDelete(DeleteBehavior.NoAction);
    }
}

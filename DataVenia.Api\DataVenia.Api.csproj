﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
	<TargetFramework>net9.0</TargetFramework>
    <UserSecretsId>48ba4806-cd46-4e3b-802d-a1ba5f7ec85c</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="8.0.2" />
	  <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
	  <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Enrichers.AspNetCore" Version="1.0.0" />
    <PackageReference Include="Serilog.Enrichers.HttpContext" Version="8.0.9" />
    <PackageReference Include="Serilog.Sinks.Seq" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DataVenia.Common.SeedDatabase\DataVenia.Common.SeedDatabase.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.AssociationHub.Application\DataVenia.Modules.AssociationHub.Application.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.AssociationHub.Infrastructure\DataVenia.Modules.AssociationHub.Infrastructure.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.AssociationHub.Presentation\DataVenia.Modules.AssociationHub.Presentation.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.Calendar.Infrastructure\DataVenia.Modules.Calendar.Infrastructure.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.Harvey.Infrastructure\DataVenia.Modules.Harvey.Infrastructure.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.Lawsuits.Infrastructure\DataVenia.Modules.Lawsuits.Infrastructure.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.Lawsuits.Worker\DataVenia.Modules.Lawsuits.Worker.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.LawsuitSync.Infrastructure\DataVenia.Modules.LawsuitSync.Infrastructure.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.LawsuitSync.Worker\DataVenia.Modules.LawsuitSync.Worker.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.Notification.Worker\DataVenia.Modules.Notification.Worker.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.Users.Infrastructure\DataVenia.Modules.Users.Infrastructure.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.Users.Worker\DataVenia.Modules.Users.Worker.csproj" />
  </ItemGroup>


</Project>

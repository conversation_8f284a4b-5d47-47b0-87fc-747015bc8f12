﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Harvey.Application.Forum;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Harvey.Presentation.Forum;
internal sealed class GetForums : IEndpoint
{
    private readonly ILogger<GetForums> _logger;

    public GetForums(ILogger<GetForums> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("/harvey/forums", async (string? displayName, ISender sender) =>
        {
            try
            {
                Result<IReadOnlyCollection<GetForumsResponse>> result = await sender.Send(new GetForumsQuery(displayName));

                return result.Match(
                    success =>
                    {
                        var responseObject = new
                        {
                            items = success
                        };

                        return Results.Ok(responseObject);
                    },
                    ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting forums");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("system:harvey:read")
        .WithTags(Tags.Harvey);
    }
}

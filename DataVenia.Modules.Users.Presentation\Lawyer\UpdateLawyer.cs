﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Lawyer.UpdateLawyer;
using DataVenia.Modules.Users.Domain.SharedModels;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
namespace DataVenia.Modules.Users.Presentation.Lawyer;

internal sealed class UpdateLawyer : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPut("lawyers", async (Request request, ClaimsPrincipal claims, ISender sender) =>
        {
            Result result = await sender.Send(new UpdateLawyerCommand(
                claims.GetUserId(),
                request.FirstName,
                request.LastName,
                request.Oabs,
                request.Contacts,
                request.Cpf,
                request.Rg,
                request.Cnh,
                request.Passport,
                request.Ctps,
                request.Pis,
                request.VoterId));

            return result.Match(Results.NoContent, ApiResults.Problem);
        })
        .RequireAuthorization("system:users:update")
        .WithTags(Tags.Users);
    }

    public sealed class Request
    {
        public string? FirstName { get; init; }

        public string? LastName { get; init; }
        public List<string>? Oabs { get; init; }
        public List<Contact>? Contacts { get; init; }
        public string? Cpf { get; init; }
        public string? Rg { get; init; }
        public string? Cnh { get; init; }
        public string? Passport { get; init; }
        public string? Ctps { get; init; }
        public string? Pis { get; init; }
        public string? VoterId { get; init; }
    }
}

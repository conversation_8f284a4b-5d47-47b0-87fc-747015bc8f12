﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Office.InviteLawyerToOffice;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Presentation.Office;

internal sealed class InviteLawyerToOffice : IEndpoint
{
    private readonly ILogger<InviteLawyerToOffice> _logger;

    public InviteLawyerToOffice(ILogger<InviteLawyerToOffice> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        try
        {
            routeBuilder.MapPost("offices/{officeId}/invites/", async (Guid officeId, ClaimsPrincipal claims, InviteRequest request, ISender sender) =>
            {
                Result result = await sender.Send(new InviteLawyerToOfficeCommand(
                    officeId,
                    request.Email,
                    claims.GetUserId()));

                return result.Match(() => Results.Ok(), ApiResults.Problem);
            })
            .RequireAuthorization("office:invites:create")
            .WithTags(Tags.OfficeInvite);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error mapping endpoint");
        }
    }

}

public sealed class InviteRequest
{
    public string Email { get; init; }
}

﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Harvey.Application.CourtDivision;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Harvey.Presentation.CourtDivision;

internal sealed class GetCourtDivisions : IEndpoint
{
    private readonly ILogger<GetCourtDivisions> _logger;

    public GetCourtDivisions(ILogger<GetCourtDivisions> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("/harvey/court-divisions", async (string? displayName, ISender sender) =>
        {
            try
            {
                Result<IReadOnlyCollection<GetCourtDivisionsResponse>> result = await sender.Send(new GetCourtDivisionsQuery(displayName));

                return result.Match(
                    success =>
                    {
                        var responseObject = new
                        {
                            items = success
                        };

                        return Results.Ok(responseObject);
                    },
                    ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting court divisions");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("system:harvey:read")
        .WithTags(Tags.Harvey);
    }
}

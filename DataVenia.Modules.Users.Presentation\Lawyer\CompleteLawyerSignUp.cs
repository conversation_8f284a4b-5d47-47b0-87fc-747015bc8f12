﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Lawyer.CompleteLawyerSignUp;
using DataVenia.Modules.Users.Application.Lawyer.StartLawyerSignUp;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.SharedModels;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Lawyer;

public class CompleteLawyerSignUp : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/lawyers/sign-up/{signUpToken}", async (Guid signUpToken, CompleteLawyerSignUpRequest request, ClaimsPrincipal claims, ISender sender) =>
            {
                Result<Guid> result = await sender.Send(new CompleteLawyerSignUpCommand(
                    signUpToken,
                    request.Password,
                    request.FirstName,
                    request.LastName,
                    request.Contacts,
                    request.Oabs,
                    request.Cpf,
                    request.Rg,
                    request.Cnh,
                    request.Passport,
                    request.Ctps,
                    request.Pis,
                    request.VoterId));

                return result.Match(id => Results.Ok(new { Id = id }), ApiResults.Problem);
            })
            .AllowAnonymous()
            .WithTags(Tags.Users);
    }

}
public sealed record CompleteLawyerSignUpRequest
{
    public string Password { get; init; }
    public string FirstName { get; init; }
    public string LastName { get; init; }
    public List<string> Oabs { get; init; }
    public List<Contact> Contacts { get; init; }
    public string Cpf { get; init; }
    public string? Rg { get; init; }
    public string? Cnh { get; init; }
    public string? Passport { get; init; }
    public string? Ctps { get; init; }
    public string? Pis { get; init; }
    public string? VoterId { get; init; }
}

using DataVenia.Modules.AssociationHub.Application.Abstractions.Data;
using DataVenia.Modules.AssociationHub.Application.Services;
using DataVenia.Modules.AssociationHub.Domain.AssociationRequest;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.AssociationHub.Infrastructure.Workers;

public sealed class AssociationProcessingWorker(
    IServiceScopeFactory serviceScopeFactory,
    ILogger<AssociationProcessingWorker> logger) : BackgroundService
{
    private readonly TimeSpan _processingInterval = TimeSpan.FromSeconds(10);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("Association Processing Worker started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessPendingRequestsAsync(stoppingToken);
                await ProcessFailedRequestsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while processing association requests");
            }

            await Task.Delay(_processingInterval, stoppingToken);
        }

        logger.LogInformation("Association Processing Worker stopped");
    }

    private async Task ProcessPendingRequestsAsync(CancellationToken cancellationToken)
    {
        using var scope = serviceScopeFactory.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IAssociationRequestRepository>();
        var processingService = scope.ServiceProvider.GetRequiredService<IAssociationProcessingService>();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        var pendingRequests = await repository.GetPendingRequestsAsync(cancellationToken);

        foreach (var request in pendingRequests)
        {
            try
            {
                request.MarkAsProcessing();
                await repository.UpdateAsync(request, cancellationToken);
                await unitOfWork.SaveChangesAsync(cancellationToken);

                var result = await processingService.ProcessAssociationRequestAsync(request, cancellationToken);

                if (result.IsSuccess)
                {
                    request.MarkAsCompleted();
                    logger.LogDebug("Successfully processed association request {RequestId}", request.Id);
                }
                else
                {
                    request.MarkAsFailed(result.Errors.FirstOrDefault()?.Message ?? "Unknown error");
                    logger.LogWarning("Failed to process association request {RequestId}: {Error}",
                        request.Id, result.Errors);
                }

                await repository.UpdateAsync(request, cancellationToken);
                await unitOfWork.SaveChangesAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Unexpected error processing association request {RequestId}", request.Id);
                
                request.MarkAsFailed($"Unexpected error: {ex.Message}");
                await repository.UpdateAsync(request, cancellationToken);
                await unitOfWork.SaveChangesAsync(cancellationToken);
            }
        }
    }

    private async Task ProcessFailedRequestsAsync(CancellationToken cancellationToken)
    {
        using var scope = serviceScopeFactory.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IAssociationRequestRepository>();
        var processingService = scope.ServiceProvider.GetRequiredService<IAssociationProcessingService>();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        var failedRequests = await repository.GetFailedRequestsForRetryAsync(3, cancellationToken);

        foreach (var request in failedRequests.Where(r => r.CanRetry()))
        {
            try
            {
                logger.LogInformation("Retrying failed association request {RequestId} (attempt {RetryCount})",
                    request.Id, request.RetryCount + 1);

                request.MarkAsProcessing();
                await repository.UpdateAsync(request, cancellationToken);
                await unitOfWork.SaveChangesAsync(cancellationToken);

                var result = await processingService.ProcessAssociationRequestAsync(request, cancellationToken);

                if (result.IsSuccess)
                {
                    request.MarkAsCompleted();
                    logger.LogInformation("Successfully processed association request {RequestId} on retry", request.Id);
                }
                else
                {
                    request.MarkAsFailed(result.Errors.FirstOrDefault()?.Message ?? "Unknown error");
                    logger.LogWarning("Failed to process association request {RequestId} on retry: {Error}",
                        request.Id, result.Errors);
                }

                await repository.UpdateAsync(request, cancellationToken);
                await unitOfWork.SaveChangesAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Unexpected error retrying association request {RequestId}", request.Id);
                
                request.MarkAsFailed($"Unexpected error on retry: {ex.Message}");
                await repository.UpdateAsync(request, cancellationToken);
                await unitOfWork.SaveChangesAsync(cancellationToken);
            }
        }
    }
}

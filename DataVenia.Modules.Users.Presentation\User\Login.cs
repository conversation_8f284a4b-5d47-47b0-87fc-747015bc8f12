﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.User.Login;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
namespace DataVenia.Modules.Users.Presentation.User;

public sealed class Login : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("login", async (HttpRequest request, ISender sender, HttpContext http) =>
        {
            if (!request.HasFormContentType)
                return Results.BadRequest("Content type must be x-www-form-urlencoded.");

            IFormCollection form = await request.ReadFormAsync();
            string username = form["Username"];
            string password = form["Password"];
            
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                return RedirectWithError(username);
            
            Result<LoginResponse> result = await sender.Send(new LoginCommand(
                username!,
                password!));

            return result.Match(
                loginResponse =>
                {
                    http.Response.Cookies.Append("AccessToken", loginResponse.AccessToken, new CookieOptions
                    {
                        HttpOnly = true,                                // Torna o cookie inacessível ao JavaScript
                        Secure = false,                                  // Garante que o cookie só seja enviado por conexões HTTPS
                        SameSite = SameSiteMode.Strict,                 // Restringe o envio do cookie apenas em navegações do mesmo domínio
                        Expires = DateTime.UtcNow.AddDays(7)        // Tempo de expiração
                    });

                    // Configurar Refresh Token como cookie HttpOnly
                    http.Response.Cookies.Append("RefreshToken", loginResponse.RefreshToken, new CookieOptions
                    {
                        HttpOnly = true,
                        Secure = false,
                        SameSite = SameSiteMode.Strict,
                        Expires = DateTime.UtcNow.AddDays(7)
                    });         

                    return Results.Redirect("/app", permanent: false, preserveMethod: false);
                },
                problem =>
                {
                    // Compute base64 of username
                    return RedirectWithError(username);
                }
            );
        })
        .AllowAnonymous()
        .WithTags(Tags.Users);
    }

    private static IResult RedirectWithError(string? username)
    {
        return Results.Redirect(
            string.IsNullOrEmpty(username)
                ? "/login?auth=error"
                : $"/login?auth=error&state={Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(username))}",
            permanent: false,
            preserveMethod: false);
    }
    
    public sealed record LoginRequest
    {
        public string Username { get; init; }
        public string Password { get; init; }
    }
}

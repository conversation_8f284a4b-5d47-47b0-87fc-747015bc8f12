﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuitById;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;
using DataVenia.Modules.Users.Presentation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Lawsuits.Presentation.Lawsuits;

public sealed class GetLawsuitById : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("offices/{officeId}/lawsuits/{lawsuitId}", async (Guid officeId, Guid lawsuitId, ClaimsPrincipal claims, ISender sender) =>
            {
                Result<EnrichedLawsuitResponse?> result = await sender.Send(new GetLawsuitByIdQuery(
                    claims.GetUserId(),
                    officeId,
                    lawsuitId));

                return result.Match(lawsuit => Results.Ok(new {item = lawsuit}), ApiResults.Problem);
            })
            .RequireAuthorization("office:lawsuits:read")
            .WithTags(Tags.Lawsuits);
    }
}

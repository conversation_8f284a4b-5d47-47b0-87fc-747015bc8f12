﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.ClientCompany.UpdateClientCompany;
using DataVenia.Modules.Users.Domain.ClientCompany;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Users.Presentation.ClientCompany;

public class UpdateClientCompany : IEndpoint
{
    private readonly ILogger<UpdateClientCompany> _logger;

    public UpdateClientCompany(ILogger<UpdateClientCompany> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        try
        {
            routeBuilder.MapPatch("offices/{officeId}/client-companies/{clientCompanyId}", async (Guid officeId, Guid clientCompanyId, ClaimsPrincipal claims, Request request, ISender sender) =>
                {
                    Result result = await sender.Send(new UpdateClientCompanyPatchCommand(
                        clientCompanyId,
                        request.Role));

                    return result.Match(Results.NoContent, ApiResults.Problem);
                })
                .RequireAuthorization("office:clients:update")
                .WithTags(Tags.OfficeInvite);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error mapping endpoint");
        }
    }

    internal sealed class Request
    {
        public ClientCompanyRole? Role { get; init; }
    }
}

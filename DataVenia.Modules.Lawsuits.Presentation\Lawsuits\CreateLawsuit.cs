﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.CreateLawsuit;
using DataVenia.Modules.Lawsuits.Domain.DTOs;
using DataVenia.Modules.Users.Presentation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Presentation.Lawsuits;

internal sealed class CreateLawsuit : IEndpoint
{
    private readonly ILogger<CreateLawsuit> _logger;

    public CreateLawsuit(ILogger<CreateLawsuit> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        // passar officeId
        routeBuilder.MapPost("/offices/{officeId}/lawsuits", async (Guid officeId, CreateLawsuitRequest request, ClaimsPrincipal claims, ISender sender) =>
            {
                try
                {
                    Result<Guid> result = await sender.Send(new CreateLawsuitCommand(
                        request.Title,
                        request.FolderId,
                        request.Parties,
                        request.LawsuitTypeId,
                        request.ClassId,
                        request.LawsuitStatusId,
                        request.LegalInstanceId,
                        request.TopicIds,
                        request.JudgingOrganId,
                        request.Cnj,
                        request.JudgingOrganHref,
                        request.Description,
                        request.Observations,
                        request.CauseValue,
                        request.ConvictionValue,
                        request.DistributedAt,
                        request.ResponsibleIds,
                        request.Access,
                        request.EvolvedFromCaseId,
                        request.GroupingCaseId,
                        claims.GetUserId(),
                        officeId));

                    return result.Match(id => Results.Ok(new
                    {
                        Id = id
                    }), ApiResults.Problem);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating lawsuit");
                }

                return Results.NotFound();
            })
            .RequireAuthorization("office:lawsuits:create")
            .WithTags(Tags.Lawsuits);
    }

}

public sealed class CreateLawsuitRequest
{
    public string Title { get; init; }
    public string Cnj { get; init; }
    public Guid? FolderId { get; init; }
    public List<PartyDto> Parties { get; init; }
    public string LawsuitTypeId { get; init; }
    public string ClassId { get; init; }
    public string LawsuitStatusId { get; init; }
    public string LegalInstanceId { get; init; }
    public List<int> TopicIds { get; init; }
    public string JudgingOrganId { get; init; }
    public string JudgingOrganHref { get; init; }
    public string Description { get; init; }
    public string Observations { get; init; }
    public decimal CauseValue { get; init; }
    public decimal ConvictionValue { get; init; }
    public DateTime DistributedAt { get; init; }
    public List<Guid> ResponsibleIds { get; init; }
    public string Access { get; init; }
    public Guid? EvolvedFromCaseId { get; init; }
    public Guid? GroupingCaseId { get; init; }
}

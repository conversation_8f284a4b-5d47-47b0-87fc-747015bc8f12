﻿using DataVenia.Common.Application.Caching;
using DataVenia.Common.Application.Clock;
using DataVenia.Common.Application.Data;
using DataVenia.Common.Application.EventBus;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Infrastructure.Authorization;
using DataVenia.Common.Infrastructure.Caching;
using DataVenia.Common.Infrastructure.Clock;
using DataVenia.Common.Infrastructure.Data;
using DataVenia.Common.Infrastructure.Interceptors;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Npgsql;
using StackExchange.Redis;

namespace DataVenia.Common.Infrastructure;

public static class InfrastructureConfiguration
{
    public static IServiceCollection AddInfrastructure(
            this IServiceCollection services,
            IConfiguration configuration)
    {
        string? databaseConnectionString = configuration.GetConnectionString("Database");

        if (string.IsNullOrWhiteSpace(databaseConnectionString))
            throw new ArgumentException($"Config {nameof(databaseConnectionString)} cannot be empty");

        string? redisConnectionString = configuration.GetConnectionString("Cache");

        if (string.IsNullOrWhiteSpace(redisConnectionString))
            throw new ArgumentException($"Config {nameof(redisConnectionString)} cannot be empty");

        services.AddHttpContextAccessor();

        services.AddAuthenticationInternal();
        services.AddAuthorizationInternal();

        NpgsqlDataSource npgsqlDataSource = new NpgsqlDataSourceBuilder(databaseConnectionString).Build();
        services.TryAddSingleton(npgsqlDataSource);

        services.AddScoped<IDbConnectionFactory, DbConnectionFactory>();

        services.TryAddSingleton<PublishDomainEventsInterceptor>();

        services.TryAddSingleton<IDateTimeProvider, DateTimeProvider>();

        try
        {
            IConnectionMultiplexer connectionMultiplexer = ConnectionMultiplexer.Connect(redisConnectionString);
            services.TryAddSingleton(connectionMultiplexer);

            services.AddStackExchangeRedisCache(options =>
            options.ConnectionMultiplexerFactory = () => Task.FromResult(connectionMultiplexer));
        }
        catch
        {
            services.AddDistributedMemoryCache();
        }

        services.TryAddSingleton<ICacheService, CacheService>();

        services.TryAddSingleton<IEventBus, EventBus.EventBus>();

        return services;
    }
}

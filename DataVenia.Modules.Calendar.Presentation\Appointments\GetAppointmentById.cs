﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Calendar.Application.Appointments.GetAppointment;
using DataVenia.Modules.Calendar.Application.Appointments.GetAppointmentById;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Calendar.Presentation.Appointments;

public class GetAppointmentById: IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("offices/{officeId}/appointments/{appointmentId}", async (Guid officeId, Guid appointmentId, ClaimsPrincipal claims, ISender sender) =>
            {
                Result<AppointmentResponse?> result = await sender.Send(new GetAppointmentByIdQuery(
                    claims.GetUserId(),
                    officeId,
                    appointmentId));

                return result.Match(appointment => Results.Ok(new {item = appointment}), ApiResults.Problem);
            })
            .RequireAuthorization("office:appointments:read")
            .WithTags(Tags.Calendar);
    }
}

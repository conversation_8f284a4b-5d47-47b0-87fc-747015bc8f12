﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.UpdateLawsuit;
using DataVenia.Modules.Lawsuits.Domain.DTOs;
using DataVenia.Modules.Users.Presentation;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Presentation.Lawsuits;
internal sealed class UpdateLawsuit : IEndpoint
{
    private readonly ILogger<UpdateLawsuit> _logger;

    public UpdateLawsuit(ILogger<UpdateLawsuit> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPut("/offices/{officeId}/lawsuits/{lawsuitId}", async (Guid officeId, Guid lawsuitId, UpdateLawsuitRequest request, ClaimsPrincipal claims, ISender sender) =>
        {
            try
            {
                Result result = await sender.Send(new UpdateLawsuitCommand(
                    lawsuitId,
                    request.Title,
                    request.Id,
                    request.FolderId,
                    request.LegalInstanceId,
                    request.LawsuitStatusId,
                    request.TopicIds,
                    // request.Parties,
                    request.JudgingOrganId,
                    request.JudgingOrganHref,
                    request.Description,
                    request.Observations,
                    request.CauseValue,
                    request.ConvictionValue,
                    request.ResponsibleIds,
                    request.Access,
                    request.EvolvedFromCaseId,
                    request.GroupingCaseId,
                    claims.GetUserId(),
                    officeId));

                return result.Match(Results.NoContent, ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating lawsuit");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("office:lawsuits:update")
        .WithTags(Tags.Lawsuits);
    }

}
public sealed class UpdateLawsuitRequest
{
    public Guid Id { get; init; }
    public string Title { get; init; }
    public Guid? FolderId { get; init; }
    public string LegalInstanceId { get; init; }
    public string LawsuitStatusId { get; init; }
    public List<int> TopicIds { get; init; }
    // public List<PartyDto> Parties { get; init; }
    public string JudgingOrganId { get; init; }
    public string JudgingOrganHref { get; init; }
    public decimal CauseValue { get; init; }
    public decimal ConvictionValue { get; init; }
    public string Description { get; init; }
    public string Observations { get; init; }
    public List<Guid> ResponsibleIds { get; init; }
    public string Access { get; init; }
    public Guid? EvolvedFromCaseId { get; init; }
    public Guid? GroupingCaseId { get; init; }
}

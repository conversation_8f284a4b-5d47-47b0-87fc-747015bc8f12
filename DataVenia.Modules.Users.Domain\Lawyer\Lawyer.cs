﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.SharedModels;
using DataVenia.Modules.Users.Domain.User;
using OabDomain = DataVenia.Modules.Users.Domain.Oab.Oab;

namespace DataVenia.Modules.Users.Domain.Lawyers;
public sealed class Lawyer : Users.User
{
    public OabDomain? Oab { get; private set; }
    public List<SignUpToken.SignUpToken> SignUpToken { get; private set; }
    private Lawyer() { }

    public static Result<Lawyer> Create(string email, string firstName, string lastName, IReadOnlyCollection<string> oabs, IReadOnlyCollection<Contact> contacts, Guid? identityId = null, string? cpf = null, string? rg = null, string? cnh = null, string? passport = null, string? ctps = null, string? pis = null, string? voterId = null)
    {
        var lawyer = new Lawyer
        {
            Id = Guid.CreateVersion7(),
            Email = email,
            FirstName = firstName,
            LastName = lastName,
            IdentityId = identityId ?? null,
            Cpf = cpf ?? "",
            Rg = rg ?? "",
            Cnh = cnh ?? "",
            Passport = passport ?? "",
            Ctps = ctps ?? "",
            Pis = pis ?? "",
            VoterId = voterId ?? "",
            CreatedAt = DateTime.UtcNow
        };

        lawyer._contacts.AddRange(contacts);

        string? firstOab = oabs.FirstOrDefault();

        if (!string.IsNullOrWhiteSpace(firstOab))
        {
            Result<OabDomain> oabResult = OabDomain.Create(firstOab, lawyer.Id);

            if (oabResult.IsFailure)
                return Result.Failure<Lawyer>(oabResult.Error);

            lawyer.Oab = oabResult.Value;
        }


        lawyer.Preferences = new Preferences() { };
        lawyer.TermsAndConditions = new TermsAndConditions() { };

        lawyer.Raise(new LawyerRegisteredDomainEvent(lawyer.Id));

        return lawyer;
    }

    public static Result<Lawyer> CreateBackOffice(string email, string firstName, string lastName, IReadOnlyCollection<string> oabs, IReadOnlyCollection<Contact> contacts, Guid? identityId = null, string? cpf = null, string? rg = null, string? cnh = null, string? passport = null, string? ctps = null, string? pis = null, string? voterId = null)
    {
        Console.WriteLine($"{oabs}, {contacts}");
        var lawyer = new Lawyer
        {
            Id = Guid.CreateVersion7(),
            Email = email,
            FirstName = firstName,
            LastName = lastName,
            IdentityId = identityId ?? null,
            Cpf = cpf ?? "",
            Rg = rg ?? "",
            Cnh = cnh ?? "",
            Passport = passport ?? "",
            Ctps = ctps ?? "",
            Pis = pis ?? "",
            VoterId = voterId ?? "",
            CreatedAt = DateTime.UtcNow
        };

        lawyer._contacts.AddRange(contacts);

        string? firstOab = oabs.FirstOrDefault();

        if (!string.IsNullOrWhiteSpace(firstOab))
        {
            Result<OabDomain> oabResult = OabDomain.Create(firstOab, lawyer.Id);

            if (oabResult.IsFailure)
                return Result.Failure<Lawyer>(oabResult.Error);

            lawyer.Oab = oabResult.Value;
        }


        lawyer.Preferences = new Preferences() { };
        lawyer.TermsAndConditions = new TermsAndConditions() { };

        lawyer.Raise(new LawyerRegisteredDomainEvent(lawyer.Id));

        return lawyer;
    }

    public Result Update(string? firstName = null, string? lastName = null, IReadOnlyCollection<string>? oabs = null, IReadOnlyCollection<Contact>? contacts = null, string? cpf = null, string? rg = null, string? cnh = null, string? passport = null, string? ctps = null, string? pis = null, string? voterId = null)
    {
        FirstName = firstName ?? FirstName;
        LastName = lastName ?? LastName;
        Cpf = cpf ?? Cpf;
        Rg = rg ?? Rg;
        Cnh = cnh ?? Cnh;
        Passport = passport ?? Passport;
        Pis = pis ?? Pis;
        Ctps = ctps ?? Ctps;
        VoterId = voterId ?? VoterId;
        UpdatedAt = DateTime.UtcNow;

        Console.WriteLine($"{oabs}, {contacts}");

        Result updateOabResult = UpdateOab(oabs);

        if(updateOabResult.IsFailure)
            return Result.Failure<Lawyer>(updateOabResult.Error);

        if (contacts is not null)
            UpdateContacts(contacts);

        return Result.Success();
    }

    private Result UpdateOab(IReadOnlyCollection<string>? oabs)
    {
        if (oabs?.FirstOrDefault() is null)
            return Result.Failure<Lawyer>(new Error("Oab.Required", "Oab cannot be empty", ErrorType.Validation));

        string firstOab = oabs.First();
#pragma warning disable IDE0074
        if (Oab is null)
#pragma warning restore IDE0074
            Oab = OabDomain.Create(firstOab, Id).Value;
        else
            if(Oab.Value != firstOab)
                Oab.UpdateValue(firstOab);

        return Result.Success();
    }

    private void UpdateContacts(IReadOnlyCollection<Contact> newContacts)
    {
        var existingContactValues = new HashSet<string>(_contacts.Select(c => c.Value));

        foreach (Contact newContact in newContacts)
        {
            if (existingContactValues.Add(newContact.Value))
            {
                _contacts.Add(newContact);
            }
        }
    }
}

﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Client.CreateClient;
using DataVenia.Modules.Users.Application.ClientCompany.CreateClientCompany;
using DataVenia.Modules.Users.Domain.ClientCompany;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.ClientCompany;

public class CreateClientCompany : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/offices/{officeId}/client-companies", async (Guid officeId, CreateClientCompanyRequest request, ClaimsPrincipal claims, ISender sender) =>
            {
                Result<Guid> result = await sender.Send(new CreateClientCompanyCommand(
                    request.ClientId,
                    request.CompanyId,
                    request.Role,
                    claims.GetUserId()
                ));

                return result.Match(id => Results.Ok(new { Id = id }), ApiResults.Problem);
            })
            .RequireAuthorization("office:clients:create")
            .WithTags(Tags.Client);
    }

}
public sealed record CreateClientCompanyRequest
{
    public Guid ClientId { get; init; }
    public Guid CompanyId { get; init; }
    public ClientCompanyRole Role { get; init; }
}

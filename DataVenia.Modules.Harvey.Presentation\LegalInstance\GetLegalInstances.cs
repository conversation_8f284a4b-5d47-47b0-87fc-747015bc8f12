﻿using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Harvey.Application.LegalInstance;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Harvey.Presentation.LegalInstance;

internal sealed class GetLegalInstances : IEndpoint
{
    private readonly ILogger<GetLegalInstances> _logger;

    public GetLegalInstances(ILogger<GetLegalInstances> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("/harvey/legal-instances", async (string? displayName, ISender sender) =>
        {
            try
            {
                Result<IReadOnlyCollection<GetLegalInstancesResponse>> result = await sender.Send(new GetLegalInstancesQuery(displayName));

                return result.Match(
                    success =>
                    {
                        var responseObject = new
                        {
                            items = success
                        };

                        return Results.Ok(responseObject);
                    },
                    ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting legal instances");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("system:harvey:read")
        .WithTags(Tags.Harvey);
    }
}

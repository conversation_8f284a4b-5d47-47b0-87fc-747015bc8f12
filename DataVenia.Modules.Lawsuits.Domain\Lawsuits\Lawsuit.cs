﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using DataVenia.Modules.Lawsuits.Domain.LawsuitSteps;
using Result = FluentResults.Result;

namespace DataVenia.Modules.Lawsuits.Domain.Lawsuits;

public sealed class Lawsuit : Entity
{
    private readonly List<LawsuitData> _lawsuitDatas = [];
    private readonly List<MonitoringHistoryEntry> _monitoringHistory = [];
    private readonly List<LawsuitStep> _lawsuitSteps = [];
    private readonly List<DataDivergence> _dataDivergences = [];
    private Lawsuit() { }

    public Guid Id { get; private set; }
    public Guid OfficeId { get; private set; }
    public string Cnj { get; private set; }
    public string? LawsuitTypeId {  get; private set; } // Natureza
    public string? ClassId { get; private set; }
    public DateTime? DistributedAt { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public bool MonitoringEnabled { get; private set; }
    public bool IsFirstTimeSyncCompleted { get; private set; }
    public IReadOnlyCollection<MonitoringHistoryEntry> MonitoringHistory => _monitoringHistory.AsReadOnly();
    public IReadOnlyCollection<LawsuitData> LawsuitDatas => _lawsuitDatas.AsReadOnly();
    public ICollection<LawsuitStep> LawsuitSteps => _lawsuitSteps.AsReadOnly();
    public IReadOnlyCollection<DataDivergence> DataDivergences => _dataDivergences.AsReadOnly();

    public static Lawsuit Create(
                                string cnj,
                                Guid officeId,
                                string lawsuitTypeId,
                                string classId,
                                DateTime distributedAt
                                )
    {
        var lawsuit = new Lawsuit
        {
            Id = Guid.CreateVersion7(),
            OfficeId = officeId,
            Cnj = cnj,
            LawsuitTypeId = lawsuitTypeId,
            ClassId = classId,
            CreatedAt = DateTime.UtcNow,
            DistributedAt = distributedAt,
            MonitoringEnabled = false,
            IsFirstTimeSyncCompleted = false,
        };

        return lawsuit;
    }

    public static Lawsuit CreateByCnj(string cnj, Guid officeId, string? lawsuitTypeId, Guid userId)
    {
        var lawsuit = new Lawsuit
        {
            Id = Guid.CreateVersion7(),
            OfficeId = officeId,
            Cnj = cnj,
            LawsuitTypeId = lawsuitTypeId,
            CreatedAt = DateTime.UtcNow,
            MonitoringEnabled = false,
            IsFirstTimeSyncCompleted = false
        };

        return lawsuit;
    }

    /// <summary>
    /// Activates monitoring. Validates that any previous monitoring period is closed.
    /// </summary>
    public FluentResults.Result ActivateMonitoring(Guid userId)
    {
        // If there is an existing monitoring history and its last entry is not closed, return an error.
        if (_monitoringHistory.Any() && _monitoringHistory[^1].StoppedAt == null)
            return Result.Fail(new FluentResults.Error("Monitoring.AlreadyActive").WithMetadata("StatusCode", 400));

        MonitoringEnabled = true;
        _monitoringHistory.Add(new MonitoringHistoryEntry(DateTime.UtcNow, userId));
        return Result.Ok();
    }

    /// <summary>
    /// Deactivates monitoring. Validates that the last monitoring period is still active.
    /// </summary>
    public Result DeactivateMonitoring(Guid userId)
    {
        // If there is no monitoring history or the last entry is already closed, return an error.
        if (!_monitoringHistory.Any() || _monitoringHistory[^1].StoppedAt != null)
            return Result.Fail(new FluentResults.Error("Monitoring.NotActive").WithMetadata("StatusCode", 400));

        MonitoringEnabled = false;
        _monitoringHistory[^1].SetStoppedAt(DateTime.UtcNow, userId);
        return Result.Ok();
    }

    public void AddDataDivergence(string fieldName, string currentValue, string eventValue)
    {
        if(eventValue == null)
            return;
        
        // Check if a divergence for the same field already exists (regardless of event value)
        var existingDivergence = _dataDivergences
            .Where(d => d.FieldName == fieldName)
            .OrderByDescending(d => d.CreatedAt)
            .FirstOrDefault();

        if (existingDivergence != null)
        {
            // If the event value has changed since the last time
            if (existingDivergence.EventValue != eventValue)
            {
                // Create a new divergence instead of updating the existing one
                _dataDivergences.Add(DataDivergence.Create(fieldName, currentValue, eventValue));
            }
            else
            {
                // If the event value is the same, just update the last seen time
                existingDivergence.UpdateLastSeenTime();
            }
        }
        else
            // If no divergence exists for this field, create a new one
            _dataDivergences.Add(DataDivergence.Create(fieldName, currentValue, eventValue));
    }

    public void MarkDataDivergenceAsAnalyzed(Guid divergenceId, string analyzedBy, bool wasAccepted)
    {
        var divergence = _dataDivergences.FirstOrDefault(d => d.Id == divergenceId);
        if (divergence != null)
            divergence.MarkAsAnalyzed(analyzedBy, wasAccepted);
    }

    public void CheckAndUpdateCoverData(string instanceId, string? lawsuitTypeId, string? classId, DateTime? distributedAt,
        string? judgingOrganId = null, decimal? causeValue = null, List<string>? topicIds = null)
    {
        // Check LawsuitTypeId
        if (!string.Equals(LawsuitTypeId, lawsuitTypeId))
            if (string.IsNullOrEmpty(LawsuitTypeId))
                LawsuitTypeId = lawsuitTypeId;
            else
                AddDataDivergence("LawsuitTypeId", LawsuitTypeId ?? string.Empty, lawsuitTypeId);

        // Check ClassId
        if (!string.Equals(ClassId, classId))
            if (string.IsNullOrEmpty(ClassId))
                ClassId = classId;
            else
                AddDataDivergence("ClassId", ClassId ?? string.Empty, classId);

        // Check DistributedAt
        // Exclude both default(DateTime) and DateTime.MinValue (01/01/0001 00:00:00)
        bool isValidDistributedAt = distributedAt.HasValue &&
                                   distributedAt != default &&
                                   distributedAt != DateTime.MinValue &&
                                   distributedAt.Value.Year > 1;
        
        if (isValidDistributedAt && (!DistributedAt.HasValue || DistributedAt != distributedAt))
        {
            bool isCurrentValueEmpty = !DistributedAt.HasValue ||
                                      DistributedAt == default ||
                                      DistributedAt == DateTime.MinValue ||
                                      DistributedAt.Value.Year <= 1;

            if (isCurrentValueEmpty)
                // If our value is empty or invalid, update it
                DistributedAt = distributedAt;
            else
                // If our value is different, add a divergence
                AddDataDivergence("DistributedAt", DistributedAt?.ToString("yyyy-MM-dd") ?? string.Empty, distributedAt.Value.ToString("yyyy-MM-dd"));
        }

        // Check JudgingOrganId
        if (!string.IsNullOrEmpty(judgingOrganId))
        {
            // Get the latest lawsuit data
            var latestLawsuitData = _lawsuitDatas.OrderByDescending(d => d.CreatedAt).FirstOrDefault(x => x.LegalInstanceId == instanceId);
            if (latestLawsuitData != null && !string.IsNullOrEmpty(latestLawsuitData.JudgingOrganId) &&
                !string.Equals(latestLawsuitData.JudgingOrganId, judgingOrganId))
            {
                AddDataDivergence("JudgingOrganId", latestLawsuitData.JudgingOrganId, judgingOrganId);
            }
        }

        // Check CauseValue
        if (causeValue.HasValue && causeValue.Value > 0)
        {
            var latestLawsuitData = _lawsuitDatas.OrderByDescending(d => d.CreatedAt).FirstOrDefault(x => x.LegalInstanceId == instanceId);
            if (latestLawsuitData != null && latestLawsuitData.CauseValue.HasValue &&
                latestLawsuitData.CauseValue.Value > 0 && latestLawsuitData.CauseValue != causeValue)
            {
                AddDataDivergence("CauseValue", latestLawsuitData.CauseValue?.ToString() ?? string.Empty, causeValue.Value.ToString());
            }
        }

        // Check TopicIds
        if (topicIds != null && topicIds.Any())
        {
            var latestLawsuitData = _lawsuitDatas.OrderByDescending(d => d.CreatedAt).FirstOrDefault();
            if (latestLawsuitData != null && latestLawsuitData.TopicIds.Any())
            {
                var numericTopicIds = new List<int>();

                // Try to convert string topic IDs to integers
                foreach (var topicIdStr in topicIds)
                {
                    if (int.TryParse(topicIdStr, out int topicId))
                    {
                        numericTopicIds.Add(topicId);
                    }
                }

                if (numericTopicIds.Any())
                {
                    // Check for differences
                    var currentTopicIds = latestLawsuitData.TopicIds.ToHashSet();
                    var eventTopicIds = numericTopicIds.ToHashSet();

                    // Find topics in the event that are not in our current data
                    var missingTopics = eventTopicIds.Except(currentTopicIds).ToList();

                    if (missingTopics.Any())
                    {
                        // Add a divergence for the topics
                        var currentTopicsStr = string.Join(", ", latestLawsuitData.TopicIds);
                        var eventTopicsStr = string.Join(", ", numericTopicIds);
                        AddDataDivergence("TopicIds", currentTopicsStr, eventTopicsStr);
                    }
                }
            }
        }
    }

    public void SetFirstTimeSyncCompleted()
    {
        IsFirstTimeSyncCompleted = true;
    }
}

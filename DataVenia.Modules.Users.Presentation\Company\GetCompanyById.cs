﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Company.GetCompanies;
using DataVenia.Modules.Users.Application.Company.GetCompanyById;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Company;

public sealed class GetCompanyById: IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapGet("offices/{officeId}/companies/{companyId}", async (Guid officeId, Guid companyId, ClaimsPrincipal claims, ISender sender) =>
            {
                Result<GetCompaniesResponse?> result = await sender.Send(new GetCompanyByIdQuery(
                    companyId));

                return result.Match(company => Results.Ok(new {item = company}), ApiResults.Problem);
            })
            .RequireAuthorization("office:clients:read")
            .WithTags(Tags.Company);
    }
}

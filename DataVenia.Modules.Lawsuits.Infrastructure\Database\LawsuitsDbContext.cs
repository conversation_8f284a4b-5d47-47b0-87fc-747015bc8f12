﻿using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Application.Cases.GetCases;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;
using DataVenia.Modules.Lawsuits.Domain.Cases;
using DataVenia.Modules.Lawsuits.Domain.CasesData;
using DataVenia.Modules.Lawsuits.Domain.DTOs;
using DataVenia.Modules.Lawsuits.Domain.LawsuitResponsibles;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using DataVenia.Modules.Lawsuits.Infrastructure.CaseParty;
using DataVenia.Modules.Lawsuits.Infrastructure.CaseResponsible;
using DataVenia.Modules.Lawsuits.Infrastructure.Cases;
using DataVenia.Modules.Lawsuits.Infrastructure.CasesData;
using DataVenia.Modules.Lawsuits.Infrastructure.IntermediateClasses;
using DataVenia.Modules.Lawsuits.Infrastructure.LawsuitData;
using DataVenia.Modules.Lawsuits.Infrastructure.LawsuitResponsibles;
using DataVenia.Modules.Lawsuits.Infrastructure.Lawsuits;
using DataVenia.Modules.Lawsuits.Infrastructure.LawsuitStep;
using DataVenia.Modules.Lawsuits.Infrastructure.Outbox;
using LawsuitPartyDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitParties.LawsuitParty;
using Microsoft.EntityFrameworkCore;
using LawsuitDataDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitsData.LawsuitData;
namespace DataVenia.Modules.Lawsuits.Infrastructure.Database;

public sealed class LawsuitsDbContext(DbContextOptions<LawsuitsDbContext> options) : DbContext(options), IUnitOfWork
{
    public Guid InstanceId { get; } = Guid.NewGuid();
    internal DbSet<Lawsuit> Lawsuits { get; set; }
    internal DbSet<LawsuitDataDomain> LawsuitDatas { get; set; }
    internal DbSet<LawsuitResponsible> LawsuitResponsibles { get; set; }
    internal DbSet<LawsuitPartyDomain> LawsuitParties { get; set; }
    internal DbSet<Case> Cases { get; set; }
    internal DbSet<CaseData> CaseDatas { get; set; }
    internal DbSet<Domain.CasesResponsibles.CaseResponsible> CaseResponsibles { get; set; }
    internal DbSet<Domain.CasesParties.CaseParty> CaseParties { get; set; }
    internal DbSet<Domain.Outbox.Outbox> Outboxes { get; set; }
    internal DbSet<Domain.LawsuitSteps.LawsuitStep> LawsuitSteps { get; set; }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema(Schemas.Lawsuit);

        modelBuilder.Entity<BaseLawsuitResponse>().HasNoKey().Metadata.SetIsTableExcludedFromMigrations(true);
        modelBuilder.Entity<CaseResponse>().HasNoKey().Metadata.SetIsTableExcludedFromMigrations(true);
        modelBuilder.Entity<PartyDto>().HasNoKey().Metadata.SetIsTableExcludedFromMigrations(true);

        modelBuilder.ApplyConfiguration(new LawsuitConfiguration());
        modelBuilder.ApplyConfiguration(new LawsuitResponsibleConfiguration());
        modelBuilder.ApplyConfiguration(new LawsuitDataConfiguration());
        modelBuilder.ApplyConfiguration(new LawsuitPartyConfiguration());

        modelBuilder.ApplyConfiguration(new CaseConfiguration());
        modelBuilder.ApplyConfiguration(new CaseResponsibleConfiguration());
        modelBuilder.ApplyConfiguration(new CaseDataConfiguration());
        modelBuilder.ApplyConfiguration(new CasePartyConfiguration());
        modelBuilder.ApplyConfiguration(new OutboxConfiguration());
        modelBuilder.ApplyConfiguration(new LawsuitStepConfiguration());
    }
}
